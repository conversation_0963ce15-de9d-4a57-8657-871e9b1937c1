2025-08-08 18:53:56 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50
2025-08-08 18:53:56 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-08 18:53:56 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 18:53:56 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 18:53:56 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 18:53:56 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x000001A134A2F430>]}
2025-08-08 18:53:56 - hyperparameter_tuning - ERROR - 超参数调优过程中出错: name 'scoring' is not defined
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 开始对 DecisionTree 进行超参数调优，试验次数: 50
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x000001A136F60D30>]}
2025-08-08 18:54:05 - hyperparameter_tuning - ERROR - 超参数调优过程中出错: name 'scoring' is not defined
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x000001A136F60E50>]}
2025-08-08 18:54:05 - hyperparameter_tuning - ERROR - 超参数调优过程中出错: name 'scoring' is not defined
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 开始对 XGBoost 进行超参数调优，试验次数: 50
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x000001A136F60F70>]}
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-08 18:54:05 - hyperparameter_tuning - ERROR - 超参数调优过程中出错: name 'scoring' is not defined
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 开始对 LightGBM 进行超参数调优，试验次数: 50
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x000001A136F770D0>]}
2025-08-08 18:54:05 - hyperparameter_tuning - ERROR - 超参数调优过程中出错: name 'scoring' is not defined
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 开始对 CatBoost 进行超参数调优，试验次数: 50
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x000001A136F60EE0>]}
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-08 18:54:05 - hyperparameter_tuning - ERROR - 超参数调优过程中出错: name 'scoring' is not defined
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 开始对 Logistic 进行超参数调优，试验次数: 50
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x000001A136F60D30>]}
2025-08-08 18:54:05 - hyperparameter_tuning - ERROR - 超参数调优过程中出错: name 'scoring' is not defined
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 开始对 SVM 进行超参数调优，试验次数: 50
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x000001A134A2F430>]}
2025-08-08 18:54:05 - hyperparameter_tuning - ERROR - 超参数调优过程中出错: name 'scoring' is not defined
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 开始对 KNN 进行超参数调优，试验次数: 50
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x000001A134814670>]}
2025-08-08 18:54:05 - hyperparameter_tuning - ERROR - 超参数调优过程中出错: name 'scoring' is not defined
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 开始对 NaiveBayes 进行超参数调优，试验次数: 50
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - NaiveBayes模型无需调参
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 开始对 NeuralNet 进行超参数调优，试验次数: 50
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 18:54:05 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x000001A136F77160>]}
2025-08-08 18:54:05 - hyperparameter_tuning - ERROR - 超参数调优过程中出错: name 'scoring' is not defined
