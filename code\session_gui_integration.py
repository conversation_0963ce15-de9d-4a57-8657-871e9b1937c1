#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
会话管理与GUI界面集成模块
处理会话恢复后的GUI界面更新和数据同步
"""

import os
import tkinter as tk
from pathlib import Path
from typing import Dict, List, Optional, Any
import logging

# 尝试导入相关模块
try:
    from config import CACHE_PATH, MODEL_NAMES, MODEL_DISPLAY_NAMES
    from logger import get_default_logger
    from session_utils import get_session_summary, get_active_session_id
except ImportError:
    # 如果导入失败，使用默认配置
    PROJECT_ROOT = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    CACHE_PATH = PROJECT_ROOT / 'cache'
    MODEL_NAMES = ['DecisionTree', 'RandomForest', 'XGBoost', 'LightGBM', 'CatBoost',
                   'Logistic', 'SVM', 'KNN', 'NaiveBayes', 'NeuralNet']
    MODEL_DISPLAY_NAMES = {name: name for name in MODEL_NAMES}
    
    def get_default_logger(name):
        logger = logging.getLogger(name)
        logger.setLevel(logging.INFO)
        if not logger.handlers:
            handler = logging.StreamHandler()
            handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
            logger.addHandler(handler)
        return logger
    
    def get_session_summary(session_id):
        return None
    
    def get_active_session_id():
        return None

logger = get_default_logger("session_gui_integration")


class SessionGUIIntegration:
    """会话管理与GUI界面集成类"""
    
    def __init__(self, main_gui):
        """
        初始化集成模块
        
        Args:
            main_gui: 主GUI界面对象
        """
        self.main_gui = main_gui
        self.logger = logger
    
    def refresh_after_session_restore(self, session_id: str):
        """
        会话恢复后刷新GUI界面

        Args:
            session_id: 恢复的会话ID
        """
        try:
            self.logger.info(f"开始刷新GUI界面，会话ID: {session_id}")

            # 1. 使用可视化修复器处理模型状态
            try:
                from gui_visualization_fix import create_visualization_fixer
                viz_fixer = create_visualization_fixer()
                viz_fixer.fix_gui_visualization_issues(self.main_gui)
            except ImportError:
                self.logger.warning("可视化修复器不可用，使用基本刷新方法")
                self._refresh_model_options()

            # 2. 刷新可视化选项
            self._refresh_visualization_options()

            # 3. 更新数据路径
            self._update_data_path_from_session(session_id)

            # 4. 刷新结果显示
            self._refresh_results_display()

            # 5. 更新状态信息
            self._update_status_info(session_id)

            # 6. 启用相关功能按钮
            self._enable_result_buttons()

            self.logger.info("GUI界面刷新完成")

        except Exception as e:
            self.logger.error(f"刷新GUI界面失败: {e}")
    
    def _refresh_model_options(self):
        """刷新模型选择选项"""
        try:
            # 检查缓存中可用的模型
            available_models = []
            
            for model_name in MODEL_NAMES:
                cache_file = CACHE_PATH / f"{model_name}_results.joblib"
                if cache_file.exists():
                    available_models.append(model_name)
            
            # 更新可视化模型选择下拉框
            if hasattr(self.main_gui, 'viz_model_var') and hasattr(self.main_gui, 'viz_model_combo'):
                current_value = self.main_gui.viz_model_var.get()
                self.main_gui.viz_model_combo['values'] = available_models
                
                # 如果当前选择的模型不在可用列表中，选择第一个可用模型
                if current_value not in available_models and available_models:
                    self.main_gui.viz_model_var.set(available_models[0])
                elif not available_models:
                    self.main_gui.viz_model_var.set("")
            
            # 更新其他模型选择控件
            self._update_other_model_selectors(available_models)
            
            self.logger.info(f"模型选项刷新完成，可用模型: {len(available_models)} 个")
            
        except Exception as e:
            self.logger.error(f"刷新模型选项失败: {e}")
    
    def _update_other_model_selectors(self, available_models: List[str]):
        """更新其他模型选择控件"""
        try:
            # 更新模型训练选项卡中的模型复选框状态
            if hasattr(self.main_gui, 'model_vars'):
                for model_name, var in self.main_gui.model_vars.items():
                    if model_name in available_models:
                        # 如果模型已训练，可以选择显示其结果
                        pass
            
            # 更新比较分析中的模型选择
            if hasattr(self.main_gui, 'comparison_model_vars'):
                for model_name, var in self.main_gui.comparison_model_vars.items():
                    if model_name in available_models:
                        var.set(True)  # 自动选择已训练的模型进行比较
            
        except Exception as e:
            self.logger.error(f"更新其他模型选择控件失败: {e}")
    
    def _refresh_visualization_options(self):
        """刷新可视化选项"""
        try:
            # 检查是否有可用模型
            has_models = any((CACHE_PATH / f"{model}_results.joblib").exists() for model in MODEL_NAMES)
            
            # 启用/禁用可视化按钮
            viz_buttons = []
            
            # 查找可视化相关按钮
            if hasattr(self.main_gui, 'root'):
                for widget in self.main_gui.root.winfo_children():
                    self._find_viz_buttons(widget, viz_buttons)
            
            # 更新按钮状态
            for button in viz_buttons:
                try:
                    button.config(state=tk.NORMAL if has_models else tk.DISABLED)
                except:
                    pass
            
            self.logger.info(f"可视化选项刷新完成，模型可用: {has_models}")
            
        except Exception as e:
            self.logger.error(f"刷新可视化选项失败: {e}")
    
    def _find_viz_buttons(self, widget, button_list):
        """递归查找可视化相关按钮"""
        try:
            # 检查当前控件
            if hasattr(widget, 'cget'):
                text = widget.cget('text') if hasattr(widget, 'cget') else ""
                if any(keyword in text for keyword in ['可视化', '比较', '报告', '图表']):
                    button_list.append(widget)
            
            # 递归检查子控件
            for child in widget.winfo_children():
                self._find_viz_buttons(child, button_list)
                
        except:
            pass
    
    def _update_data_path_from_session(self, session_id: str):
        """从会话信息更新数据路径"""
        try:
            summary = get_session_summary(session_id)
            if summary and summary.get('data_files'):
                last_data_file = summary['data_files'][-1]['data_path']
                
                # 更新主GUI的数据路径
                if hasattr(self.main_gui, 'current_data_path'):
                    self.main_gui.current_data_path.set(last_data_file)
                    self.logger.info(f"数据路径已更新: {last_data_file}")
                
                # 如果有数据预览，也更新
                if hasattr(self.main_gui, 'data_preview_text'):
                    self.main_gui.data_preview_text.delete(1.0, tk.END)
                    self.main_gui.data_preview_text.insert(tk.END, f"数据文件: {last_data_file}\n")
                    self.main_gui.data_preview_text.insert(tk.END, "数据已从会话恢复，可以开始分析\n")
            
        except Exception as e:
            self.logger.error(f"更新数据路径失败: {e}")
    
    def _refresh_results_display(self):
        """刷新结果显示"""
        try:
            # 清空现有结果显示
            if hasattr(self.main_gui, 'result_text'):
                self.main_gui.result_text.delete(1.0, tk.END)
                self.main_gui.result_text.insert(tk.END, "会话已恢复，模型结果可用\n")
                self.main_gui.result_text.insert(tk.END, "请在可视化选项卡中查看详细结果\n")
            
            # 更新训练进度显示
            if hasattr(self.main_gui, 'training_progress'):
                self.main_gui.training_progress.set(100)  # 显示为已完成
            
            # 更新状态文本
            if hasattr(self.main_gui, 'status_text'):
                self.main_gui.status_text.set("会话已恢复，模型结果可用")
            
        except Exception as e:
            self.logger.error(f"刷新结果显示失败: {e}")
    
    def _update_status_info(self, session_id: str):
        """更新状态信息"""
        try:
            # 获取会话摘要
            summary = get_session_summary(session_id)
            if summary:
                model_count = summary['statistics']['total_models']
                plot_count = summary['statistics']['total_plots']
                session_name = summary['basic_info']['session_name']
                
                # 记录日志信息
                if hasattr(self.main_gui, 'log_message'):
                    self.main_gui.log_message(f"已恢复会话: {session_name}")
                    self.main_gui.log_message(f"包含 {model_count} 个模型和 {plot_count} 个图表")
                    self.main_gui.log_message("可以在结果可视化选项卡中查看详细结果")
                
                # 更新窗口标题
                if hasattr(self.main_gui, 'root'):
                    base_title = "多模型集成机器学习平台"
                    self.main_gui.root.title(f"{base_title} - 已恢复: {session_name}")
            
        except Exception as e:
            self.logger.error(f"更新状态信息失败: {e}")
    
    def _enable_result_buttons(self):
        """启用结果相关按钮"""
        try:
            # 启用可视化相关按钮
            button_names = [
                'single_model_visualization', 'model_comparison', 
                'generate_performance_report', 'view_ensemble_results'
            ]
            
            for button_name in button_names:
                if hasattr(self.main_gui, button_name):
                    button = getattr(self.main_gui, button_name)
                    if hasattr(button, 'config'):
                        button.config(state=tk.NORMAL)
            
        except Exception as e:
            self.logger.error(f"启用结果按钮失败: {e}")
    
    def get_available_models(self) -> List[str]:
        """获取当前可用的模型列表"""
        available_models = []
        
        try:
            for model_name in MODEL_NAMES:
                cache_file = CACHE_PATH / f"{model_name}_results.joblib"
                if cache_file.exists():
                    available_models.append(model_name)
        except Exception as e:
            self.logger.error(f"获取可用模型列表失败: {e}")
        
        return available_models
    
    def get_model_performance_summary(self) -> Dict[str, Any]:
        """获取模型性能摘要"""
        summary = {}

        try:
            from joblib import load

            for model_name in self.get_available_models():
                cache_file = CACHE_PATH / f"{model_name}_results.joblib"
                try:
                    data = load(cache_file)

                    # 检查数据完整性
                    if (isinstance(data, dict) and 'y_true' in data and 'y_pred' in data and
                        data['y_true'] is not None and data['y_pred'] is not None):

                        from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score

                        y_true = data['y_true']
                        y_pred = data['y_pred']

                        summary[model_name] = {
                            'accuracy': accuracy_score(y_true, y_pred),
                            'precision': precision_score(y_true, y_pred, average='weighted'),
                            'recall': recall_score(y_true, y_pred, average='weighted'),
                            'f1_score': f1_score(y_true, y_pred, average='weighted'),
                            'data_available': True
                        }
                    else:
                        # 即使数据不完整，也记录模型存在
                        summary[model_name] = {
                            'accuracy': None,
                            'precision': None,
                            'recall': None,
                            'f1_score': None,
                            'data_available': False,
                            'note': '模型已恢复，但测试数据不完整'
                        }

                except Exception as e:
                    self.logger.warning(f"获取模型 {model_name} 性能失败: {e}")
                    # 记录模型存在但有错误
                    summary[model_name] = {
                        'accuracy': None,
                        'precision': None,
                        'recall': None,
                        'f1_score': None,
                        'data_available': False,
                        'error': str(e)
                    }

        except Exception as e:
            self.logger.error(f"获取模型性能摘要失败: {e}")

        return summary
    
    def show_session_restore_success_dialog(self, session_name: str, model_count: int, plot_count: int):
        """显示会话恢复成功对话框"""
        try:
            import tkinter.messagebox as messagebox
            
            message = f"""会话恢复成功！

会话名称: {session_name}
恢复的模型: {model_count} 个
恢复的图表: {plot_count} 个

现在您可以：
1. 在"结果可视化"选项卡中查看模型性能
2. 使用"单模型可视化"查看详细图表
3. 使用"模型比较"对比不同模型
4. 生成完整的性能报告

GUI界面已自动更新，所有功能现在可用。"""
            
            messagebox.showinfo("会话恢复成功", message)
            
        except Exception as e:
            self.logger.error(f"显示成功对话框失败: {e}")


def create_session_gui_integration(main_gui):
    """创建会话GUI集成实例的工厂函数"""
    return SessionGUIIntegration(main_gui)
