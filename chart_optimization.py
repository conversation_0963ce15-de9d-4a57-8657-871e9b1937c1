#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图表渲染优化配置模块
解决图表预览中的渲染混乱和性能问题
"""

import matplotlib
import matplotlib.pyplot as plt
import numpy as np
import warnings

# 忽略matplotlib警告
warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')

class ChartOptimizer:
    """图表渲染优化器"""
    
    def __init__(self):
        self.is_optimized = False
        self.original_settings = {}
        
    def optimize_matplotlib(self):
        """优化matplotlib设置"""
        if self.is_optimized:
            return
            
        try:
            # 保存原始设置
            self.original_settings = {
                'backend': matplotlib.get_backend(),
                'interactive': matplotlib.is_interactive(),
                'rcParams': dict(plt.rcParams)
            }
            
            # 设置后端
            matplotlib.use('TkAgg', force=True)
            
            # 关闭交互模式以提高性能
            plt.ioff()
            
            # 优化渲染设置
            plt.rcParams.update({
                # 图形设置
                'figure.facecolor': 'white',
                'figure.edgecolor': 'none',
                'axes.facecolor': 'white',
                'axes.edgecolor': 'black',
                'axes.linewidth': 0.8,
                'savefig.facecolor': 'white',
                'savefig.edgecolor': 'none',
                
                # DPI设置
                'figure.dpi': 100,
                'savefig.dpi': 300,
                
                # 字体设置
                'font.size': 10,
                'font.family': 'sans-serif',
                'font.sans-serif': ['SimHei', 'Microsoft YaHei', 'DejaVu Sans', 'Arial'],
                'axes.unicode_minus': False,
                
                # 标题和标签大小
                'axes.titlesize': 12,
                'axes.labelsize': 10,
                'xtick.labelsize': 9,
                'ytick.labelsize': 9,
                'legend.fontsize': 9,
                
                # 网格设置
                'axes.grid': True,
                'grid.alpha': 0.3,
                'grid.linewidth': 0.5,
                
                # 线条设置
                'lines.linewidth': 1.5,
                'lines.markersize': 6,
                
                # 性能优化
                'path.simplify': True,
                'path.simplify_threshold': 0.1,
                'agg.path.chunksize': 10000,
                
                # 动画设置（关闭以提高性能）
                'animation.html': 'none',
                'animation.writer': 'pillow',
                
                # 图例设置
                'legend.frameon': True,
                'legend.fancybox': True,
                'legend.shadow': False,
                'legend.framealpha': 0.8,
                
                # 颜色设置
                'axes.prop_cycle': plt.cycler('color', [
                    '#1f77b4', '#ff7f0e', '#2ca02c', '#d62728',
                    '#9467bd', '#8c564b', '#e377c2', '#7f7f7f',
                    '#bcbd22', '#17becf'
                ])
            })
            
            self.is_optimized = True
            print("✅ Matplotlib渲染优化完成")
            
        except Exception as e:
            print(f"❌ Matplotlib优化失败: {e}")
            
    def restore_settings(self):
        """恢复原始设置"""
        if not self.is_optimized or not self.original_settings:
            return
            
        try:
            # 恢复后端
            matplotlib.use(self.original_settings['backend'])
            
            # 恢复交互模式
            if self.original_settings['interactive']:
                plt.ion()
            else:
                plt.ioff()
                
            # 恢复rcParams
            plt.rcParams.update(self.original_settings['rcParams'])
            
            self.is_optimized = False
            print("✅ Matplotlib设置已恢复")
            
        except Exception as e:
            print(f"❌ 恢复设置失败: {e}")
            
    def create_optimized_figure(self, figsize=(10, 6), dpi=100):
        """创建优化的图形对象"""
        try:
            # 确保已优化
            if not self.is_optimized:
                self.optimize_matplotlib()
                
            # 创建图形
            fig, ax = plt.subplots(figsize=figsize, dpi=dpi)
            
            # 设置图形属性
            fig.patch.set_facecolor('white')
            fig.patch.set_alpha(1.0)
            
            # 设置坐标轴属性
            ax.set_facecolor('white')
            ax.grid(True, alpha=0.3, linewidth=0.5)
            ax.spines['top'].set_visible(False)
            ax.spines['right'].set_visible(False)
            
            return fig, ax
            
        except Exception as e:
            print(f"❌ 创建优化图形失败: {e}")
            return plt.subplots(figsize=figsize)
            
    def optimize_figure_for_gui(self, fig):
        """为GUI显示优化图形"""
        try:
            # 设置紧凑布局
            fig.tight_layout(pad=2.0)
            
            # 优化子图间距
            fig.subplots_adjust(
                left=0.1, right=0.95,
                top=0.9, bottom=0.1,
                hspace=0.3, wspace=0.3
            )
            
            # 设置背景
            fig.patch.set_facecolor('white')
            fig.patch.set_alpha(1.0)
            
            return fig
            
        except Exception as e:
            print(f"❌ GUI优化失败: {e}")
            return fig

# 全局优化器实例
_optimizer = ChartOptimizer()

def optimize_charts():
    """优化图表渲染（全局函数）"""
    _optimizer.optimize_matplotlib()

def restore_chart_settings():
    """恢复图表设置（全局函数）"""
    _optimizer.restore_settings()

def create_optimized_figure(figsize=(10, 6), dpi=100):
    """创建优化的图形（全局函数）"""
    return _optimizer.create_optimized_figure(figsize, dpi)

def optimize_for_gui(fig):
    """为GUI优化图形（全局函数）"""
    return _optimizer.optimize_figure_for_gui(fig)

def setup_smooth_rendering():
    """设置平滑渲染"""
    try:
        # 启用抗锯齿
        plt.rcParams['text.antialiased'] = True
        plt.rcParams['patch.antialiased'] = True
        plt.rcParams['lines.antialiased'] = True
        
        # 设置渲染质量
        plt.rcParams['figure.max_open_warning'] = 50
        
        # 优化内存使用
        plt.rcParams['figure.max_open_warning'] = 20
        
        print("✅ 平滑渲染设置完成")
        
    except Exception as e:
        print(f"❌ 平滑渲染设置失败: {e}")

def test_optimization():
    """测试优化效果"""
    print("🧪 测试图表渲染优化...")
    
    # 优化设置
    optimize_charts()
    setup_smooth_rendering()
    
    # 创建测试图表
    fig, ax = create_optimized_figure()
    
    # 生成测试数据
    x = np.linspace(0, 10, 100)
    y1 = np.sin(x)
    y2 = np.cos(x)
    
    # 绘制图表
    ax.plot(x, y1, label='Sin', linewidth=2)
    ax.plot(x, y2, label='Cos', linewidth=2)
    ax.set_title('优化渲染测试')
    ax.set_xlabel('X轴')
    ax.set_ylabel('Y轴')
    ax.legend()
    
    # GUI优化
    fig = optimize_for_gui(fig)
    
    # 保存测试图片
    try:
        fig.savefig('optimization_test.png', dpi=150, bbox_inches='tight')
        print("✅ 测试图表已保存为 optimization_test.png")
    except Exception as e:
        print(f"❌ 保存测试图表失败: {e}")
    
    plt.close(fig)
    
    print("✅ 优化测试完成")

if __name__ == "__main__":
    test_optimization()
