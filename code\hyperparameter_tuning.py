#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
超参数调优模块
使用Optuna优化各种机器学习模型的超参数
支持GPU加速和自动记录最佳性能
"""

import optuna
import numpy as np
from sklearn.model_selection import cross_val_score, StratifiedKFold, KFold
from sklearn.pipeline import Pipeline
from sklearn.preprocessing import StandardScaler
import warnings

# 过滤一些常见的警告以减少输出噪音
warnings.filterwarnings("ignore", category=UserWarning, module="xgboost")
warnings.filterwarnings("ignore", category=FutureWarning, module="sklearn")
from xgboost import XGBClassifier
from lightgbm import LGBMClassifier
from catboost import CatBoostClassifier
from sklearn.ensemble import RandomForestClassifier
from sklearn.tree import DecisionTreeClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.neighbors import KNeighborsClassifier
# from sklearn.naive_bayes import GaussianNB  # 朴素贝叶斯无需调参，暂不使用
from sklearn.neural_network import MLPClassifier
import time
import matplotlib.pyplot as plt
from pathlib import Path
import os

# 尝试导入配置模块
try:
    from config import (
        OUTPUT_PATH, RANDOM_SEED,
        get_optimized_gpu_config, detect_gpu_availability
    )
    # 获取优化的GPU配置
    OPTIMIZED_GPU_CONFIG = get_optimized_gpu_config()
    USE_GPU = OPTIMIZED_GPU_CONFIG.get('use_gpu', False)
    GPU_IDS = OPTIMIZED_GPU_CONFIG.get('gpu_ids', [0])
    GPU_PRECISION = OPTIMIZED_GPU_CONFIG.get('precision', 'float32')

    # 检测GPU可用性
    GPU_INFO = detect_gpu_availability()

except ImportError:
    # 默认设置
    # 通过获取当前文件的路径确定项目根目录
    PROJECT_ROOT = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    OUTPUT_PATH = PROJECT_ROOT / 'output'
    OUTPUT_PATH.mkdir(parents=True, exist_ok=True)
    USE_GPU = False
    GPU_IDS = [0]
    GPU_PRECISION = 'float32'
    GPU_INFO = {'xgboost_gpu': False, 'lightgbm_gpu': False, 'catboost_gpu': False}

# 注意：此文件使用 Optuna 进行超参数调优，不需要传统的 plot_hyperparameter_search 函数

# 定义本地的save_plot函数
def save_plot(fig, model_name, plot_type, file_name=None, close_fig=True):
    """
    保存图形到指定目录

    Args:
        fig: matplotlib图形对象
        model_name: 模型名称
        plot_type: 绘图类型
        file_name: 文件名，如果为None则自动生成
        close_fig: 保存后是否关闭图形

    Returns:
        str: 保存的文件路径
    """
    if file_name is None:
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        file_name = f"{model_name}_{plot_type}_{timestamp}.png"

    # 确保输出目录存在
    model_dir = OUTPUT_PATH / model_name
    model_dir.mkdir(parents=True, exist_ok=True)

    save_path = model_dir / file_name
    fig.savefig(save_path, dpi=150, bbox_inches='tight')

    if close_fig:
        plt.close(fig)

    return str(save_path)

# 尝试导入日志模块
try:
    from logger import get_default_logger
    logger = get_default_logger("hyperparameter_tuning")
except ImportError:
    import logging
    logger = logging.getLogger("hyperparameter_tuning")
    logger.setLevel(logging.INFO)
    handler = logging.StreamHandler()
    handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
    logger.addHandler(handler)

# 构建分层/非分层CV（根据y、样本量与最小类样本数动态设置）
def _build_cv(y, n_splits=5, random_state=None):
    if random_state is None:
        try:
            from config import RANDOM_SEED as _SEED
            random_state = _SEED
        except Exception:
            random_state = 42
    try:
        import numpy as np
        y_arr = np.asarray(y)
        # 计算每类样本数
        _, counts = np.unique(y_arr, return_counts=True)
        min_count = counts.min() if len(counts) > 0 else 0
        # 当最小类样本数 < 2 时，StratifiedKFold 会报错，降级为KFold
        if min_count < 2:
            return KFold(n_splits=max(2, min(n_splits, len(y_arr))), shuffle=True, random_state=random_state)
        # 否则使用分层K折，折数不超过最小类样本数，至少为2
        effective_splits = max(2, min(n_splits, int(min_count)))
        return StratifiedKFold(n_splits=effective_splits, shuffle=True, random_state=random_state)
    except Exception:
        # 退化为KFold
        return KFold(n_splits=max(2, n_splits), shuffle=True, random_state=random_state)

def objective_decision_tree(trial, X_train, y_train, scoring='roc_auc'):
    params = {
        "max_depth": trial.suggest_int("max_depth", 3, 10),  # 更保守的深度范围
        "min_samples_split": trial.suggest_int("min_samples_split", 10, 50),  # 更大的最小分割样本数
        "min_samples_leaf": trial.suggest_int("min_samples_leaf", 5, 25),     # 更大的叶节点最小样本数
        "criterion": trial.suggest_categorical("criterion", ["gini", "entropy"]),
        "class_weight": trial.suggest_categorical("class_weight", [None, "balanced"]),  # 处理不平衡数据
        "max_features": trial.suggest_categorical("max_features", [None, "sqrt", "log2"]),  # 特征选择
    }
    model = DecisionTreeClassifier(**params, random_state=RANDOM_SEED)
    cv = _build_cv(y_train, n_splits=5, random_state=RANDOM_SEED)
    return cross_val_score(model, X_train, y_train, cv=cv, scoring=scoring).mean()

def objective_xgboost(trial, X_train, y_train, scoring='roc_auc'):
    params = {
        "n_estimators": trial.suggest_int("n_estimators", 50, 300),
        "max_depth": trial.suggest_int("max_depth", 2, 10),
        "learning_rate": trial.suggest_float("learning_rate", 0.01, 0.3),
        "subsample": trial.suggest_float("subsample", 0.5, 1.0),
        "colsample_bytree": trial.suggest_float("colsample_bytree", 0.5, 1.0),
    }

    # GPU配置 - 使用XGBoost 2.0+语法并检查GPU可用性
    if USE_GPU and GPU_INFO.get('xgboost_gpu', False):
        try:
            params["device"] = "cuda"
            params["tree_method"] = "hist"
            logger.info("XGBoost使用GPU加速")
        except Exception as e:
            logger.warning(f"XGBoost GPU配置失败，回退到CPU: {e}")
            params["device"] = "cpu"
            params["tree_method"] = "hist"
    else:
        params["device"] = "cpu"
        params["tree_method"] = "hist"

    model = XGBClassifier(**params, random_state=RANDOM_SEED, eval_metric='logloss')
    cv = _build_cv(y_train, n_splits=5, random_state=RANDOM_SEED)
    return cross_val_score(model, X_train, y_train, cv=cv, scoring=scoring).mean()

def objective_lightgbm(trial, X_train, y_train, scoring='roc_auc'):
    params = {
        "n_estimators": trial.suggest_int("n_estimators", 50, 300),
        "max_depth": trial.suggest_int("max_depth", 2, 10),
        "learning_rate": trial.suggest_float("learning_rate", 0.01, 0.3),
        "feature_fraction": trial.suggest_float("feature_fraction", 0.5, 1.0),
        "bagging_fraction": trial.suggest_float("bagging_fraction", 0.5, 1.0),
    }

    # GPU配置 - 检查LightGBM GPU支持
    if USE_GPU and GPU_INFO.get('lightgbm_gpu', False):
        try:
            params["device"] = "gpu"
            params["gpu_platform_id"] = 0
            params["gpu_device_id"] = GPU_IDS[0]
            logger.info("LightGBM使用GPU加速")
        except Exception as e:
            logger.warning(f"LightGBM GPU配置失败，回退到CPU: {e}")
            params["device"] = "cpu"
    else:
        params["device"] = "cpu"

    # 添加静默模式以减少输出
    params["verbose"] = -1

    model = LGBMClassifier(**params, random_state=RANDOM_SEED)  # type: ignore
    cv = _build_cv(y_train, n_splits=5, random_state=RANDOM_SEED)
    return cross_val_score(model, X_train, y_train, cv=cv, scoring=scoring).mean()  # type: ignore

def objective_catboost(trial, X_train, y_train, scoring='roc_auc'):
    params = {
        "iterations": trial.suggest_int("iterations", 50, 300),
        "depth": trial.suggest_int("depth", 2, 10),
        "learning_rate": trial.suggest_float("learning_rate", 0.01, 0.3),
        "l2_leaf_reg": trial.suggest_float("l2_leaf_reg", 1, 10),
        "bagging_temperature": trial.suggest_float("bagging_temperature", 0.0, 1.0),
    }

    # GPU配置 - 检查CatBoost GPU支持
    if USE_GPU and GPU_INFO.get('catboost_gpu', False):
        try:
            params["task_type"] = "GPU"
            params["devices"] = str(GPU_IDS[0])
            logger.info("CatBoost使用GPU加速")
        except Exception as e:
            logger.warning(f"CatBoost GPU配置失败，回退到CPU: {e}")
            params["task_type"] = "CPU"
    else:
        params["task_type"] = "CPU"

    # 设置静默模式
    params["verbose"] = False
    params["allow_writing_files"] = False

    model = CatBoostClassifier(**params, random_state=RANDOM_SEED)  # type: ignore
    cv = _build_cv(y_train, n_splits=5, random_state=RANDOM_SEED)
    return cross_val_score(model, X_train, y_train, cv=cv, scoring=scoring).mean()  # type: ignore

def objective_random_forest(trial, X_train, y_train, scoring='roc_auc'):
    params = {
        "n_estimators": trial.suggest_int("n_estimators", 50, 300),
        "max_depth": trial.suggest_int("max_depth", 2, 32),
        "min_samples_split": trial.suggest_int("min_samples_split", 2, 20),
        "min_samples_leaf": trial.suggest_int("min_samples_leaf", 1, 20),
        "max_features": trial.suggest_categorical("max_features", ["sqrt", "log2"]),
    }
    model = RandomForestClassifier(**params, random_state=RANDOM_SEED)
    cv = _build_cv(y_train, n_splits=5, random_state=RANDOM_SEED)
    return cross_val_score(model, X_train, y_train, cv=cv, scoring=scoring).mean()

def objective_logistic(trial, X_train, y_train, scoring='roc_auc'):
    params = {
        "clf__C": trial.suggest_float("clf__C", 0.1, 10.0),
        "clf__solver": trial.suggest_categorical("clf__solver", ["lbfgs", "liblinear"]),
    }
    pipe = Pipeline([
        ("scaler", StandardScaler()),
        ("clf", LogisticRegression(random_state=RANDOM_SEED, max_iter=2000))
    ])
    pipe.set_params(**params)
    cv = _build_cv(y_train, n_splits=5, random_state=RANDOM_SEED)
    return cross_val_score(pipe, X_train, y_train, cv=cv, scoring=scoring).mean()

def objective_svm(trial, X_train, y_train, scoring='roc_auc'):
    params = {
        "clf__C": trial.suggest_float("clf__C", 0.1, 10.0),
        "clf__kernel": trial.suggest_categorical("clf__kernel", ["rbf", "linear"]),
    }
    pipe = Pipeline([
        ("scaler", StandardScaler()),
        ("clf", SVC(random_state=RANDOM_SEED, probability=True))
    ])
    pipe.set_params(**params)
    cv = _build_cv(y_train, n_splits=5, random_state=RANDOM_SEED)
    return cross_val_score(pipe, X_train, y_train, cv=cv, scoring=scoring).mean()

def objective_knn(trial, X_train, y_train, scoring='roc_auc'):
    params = {
        "clf__n_neighbors": trial.suggest_int("clf__n_neighbors", 3, 10),
    }
    pipe = Pipeline([
        ("scaler", StandardScaler()),
        ("clf", KNeighborsClassifier())
    ])
    pipe.set_params(**params)
    cv = _build_cv(y_train, n_splits=5, random_state=RANDOM_SEED)
    return cross_val_score(pipe, X_train, y_train, cv=cv, scoring=scoring).mean()

def objective_neural_net(trial, X_train, y_train, scoring='roc_auc'):
    params = {
        "clf__hidden_layer_sizes": trial.suggest_categorical("clf__hidden_layer_sizes", [(50,), (100,), (50, 50)]),
        "clf__alpha": trial.suggest_float("clf__alpha", 0.0001, 0.01),
    }
    pipe = Pipeline([
        ("scaler", StandardScaler()),
        ("clf", MLPClassifier(random_state=RANDOM_SEED, max_iter=2000))
    ])
    pipe.set_params(**params)
    cv = _build_cv(y_train, n_splits=5, random_state=RANDOM_SEED)
    return cross_val_score(pipe, X_train, y_train, cv=cv, scoring=scoring).mean()

def plot_optimization_history(study, model_name):
    """
    绘制优化历史

    Args:
        study: Optuna study对象
        model_name: 模型名称
    """
    try:
        # 绘制优化历史
        fig = optuna.visualization.plot_optimization_history(study)
        fig.update_layout(title=f"{model_name} 优化历史")

        # 保存图像
        model_dir = OUTPUT_PATH / model_name
        model_dir.mkdir(parents=True, exist_ok=True)

        timestamp = time.strftime("%Y%m%d_%H%M%S")
        file_path = model_dir / f"optimization_history_{timestamp}.png"

        # 尝试使用kaleido引擎保存图像
        try:
            fig.write_image(str(file_path), engine="kaleido")
            logger.info(f"优化历史图已保存到: {file_path}")
        except Exception as e:
            logger.warning(f"使用kaleido引擎保存图像失败: {e}")
            # 尝试使用其他方法保存
            try:
                fig.write_html(str(file_path).replace('.png', '.html'))
                logger.info(f"优化历史图已保存为HTML格式到: {str(file_path).replace('.png', '.html')}")
            except Exception as e2:
                logger.warning(f"保存HTML格式也失败: {e2}")

    except Exception as e:
        logger.warning(f"绘制优化历史图失败: {e}")

def plot_param_importances(study, model_name):
    """
    绘制超参数重要性

    Args:
        study: Optuna study对象
        model_name: 模型名称
    """
    try:
        # 绘制超参数重要性
        fig = optuna.visualization.plot_param_importances(study)
        fig.update_layout(title=f"{model_name} 超参数重要性")

        # 保存图像
        model_dir = OUTPUT_PATH / model_name
        model_dir.mkdir(parents=True, exist_ok=True)

        timestamp = time.strftime("%Y%m%d_%H%M%S")
        file_path = model_dir / f"param_importances_{timestamp}.png"

        # 尝试使用kaleido引擎保存图像
        try:
            fig.write_image(str(file_path), engine="kaleido")
            logger.info(f"超参数重要性图已保存到: {file_path}")
        except Exception as e:
            logger.warning(f"使用kaleido引擎保存图像失败: {e}")
            # 尝试使用其他方法保存
            try:
                fig.write_html(str(file_path).replace('.png', '.html'))
                logger.info(f"超参数重要性图已保存为HTML格式到: {str(file_path).replace('.png', '.html')}")
            except Exception as e2:
                logger.warning(f"保存HTML格式也失败: {e2}")

    except Exception as e:
        logger.warning(f"绘制超参数重要性图失败: {e}")

def tune_model(model_name, n_trials=50, X_train=None, y_train=None, return_study=False,
               early_stopping_rounds=None, patience=10, min_improvement=0.001,
               strategy='TPE', timeout=None, n_jobs=1, scoring: str = 'roc_auc'):
    """
    对指定模型进行超参数调优

    Args:
        model_name: 模型名称
        n_trials: 试验次数
        X_train: 训练特征
        y_train: 训练标签
        return_study: 是否返回study对象用于可视化
        early_stopping_rounds: 早停轮数（如果为None则不启用早停）
        patience: 耐心值，连续多少轮没有改善就停止
        min_improvement: 最小改善阈值
        strategy: 调优策略 ('TPE', 'Random', 'CmaEs')
        timeout: 超时时间（秒）
        n_jobs: 并行作业数

    Returns:
        tuple: (最佳参数, 最佳得分) 或 (最佳参数, 最佳得分, study对象)
    """
    logger.info(f"开始对 {model_name} 进行超参数调优，试验次数: {n_trials}")
    logger.info(f"调优策略: {strategy}, 并行作业数: {n_jobs}")
    logger.info(f"调参评分指标: {scoring}")
    if timeout:
        logger.info(f"超时设置: {timeout}秒")
    if early_stopping_rounds:
        logger.info(f"启用早停机制，耐心值: {patience}，最小改善: {min_improvement}")
    start_time = time.time()

    # 根据策略选择采样器
    sampler = None
    if strategy == 'TPE':
        sampler = optuna.samplers.TPESampler(seed=RANDOM_SEED)
    elif strategy == 'Random':
        sampler = optuna.samplers.RandomSampler(seed=RANDOM_SEED)
    elif strategy == 'CmaEs':
        sampler = optuna.samplers.CmaEsSampler(seed=RANDOM_SEED)
    else:
        logger.warning(f"未知的调优策略: {strategy}，使用默认TPE策略")
        sampler = optuna.samplers.TPESampler(seed=RANDOM_SEED)

    # 创建study对象，设置方向为最大化得分
    study = optuna.create_study(direction="maximize", sampler=sampler)

    # 早停机制相关变量
    best_score_so_far = -np.inf
    no_improvement_count = 0

    # 根据模型类型选择对应的目标函数
    if model_name == "XGBoost":
        objective_func = lambda trial: objective_xgboost(trial, X_train, y_train, scoring)
    elif model_name == "LightGBM":
        objective_func = lambda trial: objective_lightgbm(trial, X_train, y_train, scoring)
    elif model_name == "CatBoost":
        objective_func = lambda trial: objective_catboost(trial, X_train, y_train, scoring)
    elif model_name == "RandomForest":
        objective_func = lambda trial: objective_random_forest(trial, X_train, y_train, scoring)
    elif model_name == "DecisionTree":
        objective_func = lambda trial: objective_decision_tree(trial, X_train, y_train, scoring)
    elif model_name == "Logistic":
        objective_func = lambda trial: objective_logistic(trial, X_train, y_train, scoring)
    elif model_name == "SVM":
        objective_func = lambda trial: objective_svm(trial, X_train, y_train, scoring)
    elif model_name == "KNN":
        objective_func = lambda trial: objective_knn(trial, X_train, y_train, scoring)
    elif model_name == "NeuralNet":
        objective_func = lambda trial: objective_neural_net(trial, X_train, y_train, scoring)
    elif model_name == "NaiveBayes":
        # NaiveBayes 无需调参，返回空参数
        logger.info("NaiveBayes模型无需调参")
        if return_study:
            return {}, 0.0, None
        return {}, 0.0
    else:
        logger.error(f"未知的模型类型: {model_name}")
        if return_study:
            return {}, 0.0, None
        return {}, 0.0

    # 定义带早停的回调函数
    def early_stopping_callback(study, trial):
        nonlocal best_score_so_far, no_improvement_count

        if early_stopping_rounds is None:
            return

        current_best = study.best_value

        # 检查是否有显著改善
        if current_best > best_score_so_far + min_improvement:
            best_score_so_far = current_best
            no_improvement_count = 0
            logger.info(f"Trial {trial.number}: 发现更好的得分 {current_best:.4f}")
        else:
            no_improvement_count += 1

        # 检查是否需要早停
        if no_improvement_count >= patience:
            logger.info(f"早停触发：连续 {patience} 轮没有显著改善（阈值: {min_improvement}）")
            logger.info(f"当前最佳得分: {current_best:.4f}")
            study.stop()

    # 运行优化
    try:
        # 设置优化参数
        optimize_kwargs = {
            'n_trials': n_trials,
            'n_jobs': n_jobs if n_jobs > 1 else 1,  # 并行作业数
        }

        # 添加超时设置
        if timeout:
            optimize_kwargs['timeout'] = timeout

        logger.info(f"开始优化，参数: {optimize_kwargs}")
        # 明确传入 callbacks，避免optuna类型推断混淆
        if early_stopping_rounds:
            study.optimize(
                objective_func,
                n_trials=int(optimize_kwargs.get('n_trials', 0)) or None,
                timeout=optimize_kwargs.get('timeout', None),
                n_jobs=int(optimize_kwargs.get('n_jobs', 1)),
                callbacks=[early_stopping_callback]
            )
        else:
            study.optimize(
                objective_func,
                n_trials=int(optimize_kwargs.get('n_trials', 0)) or None,
                timeout=optimize_kwargs.get('timeout', None),
                n_jobs=int(optimize_kwargs.get('n_jobs', 1))
            )

        # 记录最佳参数和得分
        best_params = study.best_params
        best_score = study.best_value

        # 打印结果
        logger.info(f"模型 {model_name} 的最佳参数: {best_params}")
        logger.info(f"模型 {model_name} 的最佳得分: {best_score:.4f}")

        # 如果启用了早停，记录实际试验次数
        if early_stopping_rounds:
            actual_trials = len(study.trials)
            logger.info(f"实际执行试验次数: {actual_trials}/{n_trials}")
            if actual_trials < n_trials:
                logger.info("由于早停机制，调优提前结束")

        # 绘制优化历史和参数重要性
        try:
            plot_optimization_history(study, model_name)
            plot_param_importances(study, model_name)
        except Exception as e:
            logger.warning(f"绘制优化可视化失败: {e}")

        # 记录调优耗时
        end_time = time.time()
        logger.info(f"超参数调优完成，耗时: {end_time - start_time:.2f} 秒")

        if return_study:
            return best_params, best_score, study
        return best_params, best_score

    except Exception as e:
        logger.error(f"超参数调优过程中出错: {e}")
        if return_study:
            return {}, 0.0, None
        return {}, 0.0

# 如果直接运行该脚本，执行测试
if __name__ == "__main__":
    # 生成一些随机测试数据
    np.random.seed(42)
    X_test = np.random.rand(100, 5)
    y_test = np.random.randint(0, 2, 100)

    # 测试调优XGBoost模型
    res = tune_model("XGBoost", n_trials=10, X_train=X_test, y_train=y_test)
    if isinstance(res, tuple) and len(res) >= 2:
        best_params, best_score = res[:2]
        print(f"最佳参数: {best_params}")
        print(f"最佳得分: {best_score:.4f}")
