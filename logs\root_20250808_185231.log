2025-08-08 18:52:31 - numexpr.utils - INFO - Note: NumExpr detected 16 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 8.
2025-08-08 18:52:31 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-08-08 18:52:33 - training_session_manager - INFO - 初始化训练会话管理器
2025-08-08 18:52:34 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-08 18:52:34 - main - INFO - 已确保输出目录结构存在: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\output
2025-08-08 18:52:34 - main - INFO - 比较所有模型的性能
2025-08-08 18:52:34 - plot_comparison - INFO - 加载模型 DecisionTree 的缓存结果
2025-08-08 18:52:34 - plot_comparison - INFO - 加载模型 RandomForest 的缓存结果
2025-08-08 18:52:34 - plot_comparison - INFO - 加载模型 XGBoost 的缓存结果
2025-08-08 18:52:34 - plot_comparison - INFO - 加载模型 LightGBM 的缓存结果
2025-08-08 18:52:34 - plot_comparison - INFO - 加载模型 CatBoost 的缓存结果
2025-08-08 18:52:34 - plot_comparison - INFO - 加载模型 Logistic 的缓存结果
2025-08-08 18:52:34 - plot_comparison - INFO - 加载模型 SVM 的缓存结果
2025-08-08 18:52:34 - plot_comparison - INFO - 加载模型 KNN 的缓存结果
2025-08-08 18:52:34 - plot_comparison - INFO - 加载模型 NaiveBayes 的缓存结果
2025-08-08 18:52:34 - plot_comparison - INFO - 加载模型 NeuralNet 的缓存结果
2025-08-08 18:52:34 - plot_utils - INFO - 图形已保存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\combined\dca\combined_dca.png
2025-08-08 18:52:34 - plot_utils - INFO - 图形已保存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\combined\cic\combined_cic.png
2025-08-08 18:52:34 - plot_utils - INFO - 图形已保存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\combined\roc\combined_roc.png
2025-08-08 18:52:34 - plot_utils - INFO - 图形已保存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\combined\pr\combined_pr.png
2025-08-08 18:52:35 - plot_utils - INFO - 图形已保存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\combined\calibration\combined_calibration.png
2025-08-08 18:52:35 - main - INFO - 比较图表已保存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\combined
