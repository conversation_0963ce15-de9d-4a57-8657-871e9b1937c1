2025-08-08 20:17:20 - hyperparameter_tuning - INFO - 开始对 Logistic 进行超参数调优，试验次数: 50
2025-08-08 20:17:20 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-08 20:17:20 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 20:17:20 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 20:17:20 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x0000025069C98860>]}
2025-08-08 20:17:20 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.4360
2025-08-08 20:17:20 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 20:17:20 - hyperparameter_tuning - INFO - 当前最佳得分: 0.4361
2025-08-08 20:17:20 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳参数: {'clf__C': 1.4809892204552142, 'clf__solver': 'liblinear'}
2025-08-08 20:17:20 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳得分: 0.4361
2025-08-08 20:17:20 - hyperparameter_tuning - INFO - 实际执行试验次数: 11/50
2025-08-08 20:17:20 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-08 20:17:21 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 20:17:21 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\optimization_history_20250808_201721.html
2025-08-08 20:17:21 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 20:17:21 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\param_importances_20250808_201721.html
2025-08-08 20:17:21 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.63 秒
