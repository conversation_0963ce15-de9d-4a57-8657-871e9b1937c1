#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的二分类分析流程
一键式完成从数据加载到最终报告的全流程分析
"""

import os
import time
from pathlib import Path
from datetime import datetime

from data_preprocessing import load_and_preprocess_data
from model_training import MODEL_TRAINERS
from hyperparameter_tuning import tune_model
from best_model_selector import select_best_model_for_binary_classification
from model_performance_report import generate_comprehensive_report
from plot_single_model import plot_model_visualizations
from plot_comparison import plot_model_comparison
from model_ensemble import run_ensemble_pipeline
from plot_ensemble import visualize_ensemble_results
from config import OUTPUT_PATH, MODEL_NAMES, ENSEMBLE_CONFIG
from logger import get_logger

logger = get_logger(__name__)

class BinaryClassificationPipeline:
    """
    完整的二分类分析流程
    """
    
    def __init__(self, data_path, output_dir=None, enable_tuning=True, enable_shap=True, enable_ensemble=True):
        """
        初始化分析流程

        Args:
            data_path: 数据文件路径
            output_dir: 输出目录
            enable_tuning: 是否启用超参数调优
            enable_shap: 是否启用SHAP分析
            enable_ensemble: 是否启用集成学习
        """
        self.data_path = Path(data_path)
        self.output_dir = Path(output_dir) if output_dir else OUTPUT_PATH / "binary_classification_analysis"
        self.enable_tuning = enable_tuning
        self.enable_shap = enable_shap
        self.enable_ensemble = enable_ensemble
        
        # 创建输出目录
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 分析结果存储
        self.results = {
            'data_info': {},
            'trained_models': [],
            'best_model': None,
            'performance_summary': {},
            'analysis_time': {}
        }
        
        logger.info(f"初始化二分类分析流程")
        logger.info(f"数据文件: {self.data_path}")
        logger.info(f"输出目录: {self.output_dir}")
        logger.info(f"超参数调优: {'启用' if self.enable_tuning else '禁用'}")
        logger.info(f"SHAP分析: {'启用' if self.enable_shap else '禁用'}")
    
    def step1_data_preprocessing(self):
        """
        步骤1: 数据预处理
        """
        logger.info("=" * 60)
        logger.info("步骤1: 数据预处理")
        logger.info("=" * 60)
        
        start_time = time.time()
        
        try:
            # 加载和预处理数据
            X_train, X_test, y_train, y_test = load_and_preprocess_data(
                str(self.data_path), 
                scaling_method='standard'
            )
            
            # 存储数据信息
            self.X_train, self.X_test = X_train, X_test
            self.y_train, self.y_test = y_train, y_test
            
            # 记录数据信息
            self.results['data_info'] = {
                'total_samples': len(X_train) + len(X_test),
                'train_samples': len(X_train),
                'test_samples': len(X_test),
                'features': X_train.shape[1],
                'class_distribution': {
                    'train': {
                        'class_0': int((y_train == 0).sum()),
                        'class_1': int((y_train == 1).sum())
                    },
                    'test': {
                        'class_0': int((y_test == 0).sum()),
                        'class_1': int((y_test == 1).sum())
                    }
                }
            }
            
            end_time = time.time()
            self.results['analysis_time']['data_preprocessing'] = end_time - start_time
            
            logger.info(f"数据预处理完成")
            logger.info(f"训练集: {len(X_train)} 样本, {X_train.shape[1]} 特征")
            logger.info(f"测试集: {len(X_test)} 样本")
            logger.info(f"类别分布 - 训练集: {(y_train == 0).sum()}:{(y_train == 1).sum()}")
            logger.info(f"类别分布 - 测试集: {(y_test == 0).sum()}:{(y_test == 1).sum()}")
            
            return True
            
        except Exception as e:
            logger.error(f"数据预处理失败: {e}")
            return False
    
    def step2_model_training(self, selected_models=None):
        """
        步骤2: 模型训练
        
        Args:
            selected_models: 指定要训练的模型列表，None表示训练所有模型
        """
        logger.info("=" * 60)
        logger.info("步骤2: 模型训练")
        logger.info("=" * 60)
        
        start_time = time.time()
        
        if selected_models is None:
            selected_models = MODEL_NAMES
        
        trained_count = 0
        
        for model_name in selected_models:
            try:
                logger.info(f"训练模型: {model_name}")
                
                if self.enable_tuning:
                    # 超参数调优
                    logger.info(f"  正在进行超参数调优...")
                    best_params, best_score = tune_model(
                        model_name,
                        n_trials=30,  # 减少试验次数以提高速度
                        X_train=self.X_train,
                        y_train=self.y_train,
                        early_stopping_rounds=True,  # 启用早停
                        patience=10,  # 耐心值
                        min_improvement=0.001,  # 最小改善阈值
                        strategy='TPE',  # 默认使用TPE策略
                        n_jobs=1  # 默认单线程
                    )
                    logger.info(f"  最佳参数: {best_params}")
                    logger.info(f"  最佳得分: {best_score:.4f}")
                else:
                    best_params = None
                
                # 训练模型
                trainer = MODEL_TRAINERS[model_name]
                model = trainer.train_and_evaluate(
                    self.X_train, self.y_train, 
                    self.X_test, self.y_test, 
                    params=best_params
                )
                
                self.results['trained_models'].append(model_name)
                trained_count += 1
                
                logger.info(f"  {model_name} 训练完成")
                
            except Exception as e:
                logger.error(f"训练模型 {model_name} 失败: {e}")
        
        end_time = time.time()
        self.results['analysis_time']['model_training'] = end_time - start_time
        
        logger.info(f"模型训练完成，成功训练 {trained_count} 个模型")
        return trained_count > 0
    
    def step3_best_model_selection(self, strategy='balanced'):
        """
        步骤3: 最佳模型选择
        
        Args:
            strategy: 选择策略
        """
        logger.info("=" * 60)
        logger.info("步骤3: 最佳模型选择")
        logger.info("=" * 60)
        
        start_time = time.time()
        
        try:
            # 自动选择最佳模型
            selection_result = select_best_model_for_binary_classification(
                data_path=str(self.data_path),
                strategy=strategy,
                top_k=5
            )
            
            if selection_result:
                self.results['best_model'] = selection_result
                
                end_time = time.time()
                self.results['analysis_time']['model_selection'] = end_time - start_time
                
                logger.info(f"最佳模型选择完成")
                logger.info(f"推荐模型: {selection_result['best_model']}")
                logger.info(f"综合得分: {selection_result['best_score']:.4f}")
                
                return True
            else:
                logger.error("最佳模型选择失败")
                return False
                
        except Exception as e:
            logger.error(f"最佳模型选择过程出错: {e}")
            return False

    def step4_ensemble_learning(self):
        """
        步骤4: 集成学习
        """
        if not self.enable_ensemble:
            logger.info("集成学习已禁用，跳过此步骤")
            return True

        logger.info("=" * 60)
        logger.info("步骤4: 集成学习")
        logger.info("=" * 60)

        start_time = time.time()

        try:
            # 获取训练好的模型列表
            trained_models = self.results.get('trained_models', [])

            if len(trained_models) < 2:
                logger.warning("需要至少2个训练好的模型才能进行集成学习")
                return True

            logger.info(f"使用 {len(trained_models)} 个模型进行集成学习")
            logger.info(f"模型列表: {trained_models}")

            # 运行集成学习管道
            ensemble_results = run_ensemble_pipeline(
                X_train=self.X_train,
                y_train=self.y_train,
                X_test=self.X_test,
                y_test=self.y_test,
                model_names=trained_models,
                ensemble_methods=ENSEMBLE_CONFIG['default_ensemble_methods'],
                save_results=ENSEMBLE_CONFIG['save_results'],
                output_dir=self.output_dir / 'ensemble',
                enable_shap=self.enable_shap and ENSEMBLE_CONFIG['enable_shap']
            )

            if ensemble_results:
                self.results['ensemble_results'] = ensemble_results

                # 找出最佳集成模型
                best_ensemble_name = max(ensemble_results.keys(),
                                       key=lambda x: ensemble_results[x]['metrics']['f1_score'])
                best_ensemble_metrics = ensemble_results[best_ensemble_name]['metrics']

                self.results['best_ensemble'] = {
                    'method': best_ensemble_name,
                    'metrics': best_ensemble_metrics
                }

                end_time = time.time()
                self.results['analysis_time']['ensemble_learning'] = end_time - start_time

                logger.info(f"集成学习完成")
                logger.info(f"最佳集成方法: {best_ensemble_name}")
                logger.info(f"最佳集成F1分数: {best_ensemble_metrics['f1_score']:.4f}")
                logger.info(f"最佳集成准确率: {best_ensemble_metrics['accuracy']:.4f}")

                # 生成集成学习可视化
                logger.info("生成集成学习可视化图表...")
                try:
                    visualize_ensemble_results(
                        ensemble_results=ensemble_results,
                        X_train=self.X_train,
                        y_train=self.y_train,
                        output_dir=self.output_dir / 'ensemble' / 'visualizations'
                    )
                except Exception as e:
                    logger.warning(f"集成学习可视化生成失败: {e}")

                return True
            else:
                logger.error("集成学习失败")
                return False

        except Exception as e:
            logger.error(f"集成学习过程出错: {e}")
            return False

    def step5_visualization_and_reports(self):
        """
        步骤5: 可视化和报告生成
        """
        logger.info("=" * 60)
        logger.info("步骤5: 可视化和报告生成")
        logger.info("=" * 60)
        
        start_time = time.time()
        
        try:
            # 1. 生成模型比较报告
            logger.info("生成模型性能比较报告...")
            generate_comprehensive_report(selected_models=self.results['trained_models'])
            
            # 2. 生成模型比较图表
            logger.info("生成模型比较图表...")
            try:
                plot_model_comparison(selected_models=self.results['trained_models'])
            except Exception as e:
                logger.warning(f"模型比较图表生成失败: {e}")

            # 3. 为最佳模型生成详细可视化
            if self.results['best_model']:
                best_model_name = self.results['best_model']['best_model']
                logger.info(f"为最佳模型 {best_model_name} 生成详细可视化...")

                try:
                    plot_model_visualizations(
                        model_name=best_model_name,
                        enable_shap=self.enable_shap
                    )
                except Exception as e:
                    logger.warning(f"最佳模型可视化生成失败: {e}")
            
            end_time = time.time()
            self.results['analysis_time']['visualization'] = end_time - start_time
            
            logger.info("可视化和报告生成完成")
            return True
            
        except Exception as e:
            logger.error(f"可视化和报告生成失败: {e}")
            return False
    
    def step6_generate_summary_report(self):
        """
        步骤6: 生成分析摘要报告
        """
        logger.info("=" * 60)
        logger.info("步骤6: 生成分析摘要报告")
        logger.info("=" * 60)
        
        try:
            # 计算总分析时间
            total_time = sum(self.results['analysis_time'].values())
            
            # 生成摘要报告
            summary_report = self._generate_summary_report(total_time)
            
            # 保存摘要报告
            summary_file = self.output_dir / "analysis_summary.md"
            with open(summary_file, 'w', encoding='utf-8') as f:
                f.write(summary_report)
            
            logger.info(f"分析摘要报告已保存到: {summary_file}")
            
            # 在控制台输出关键信息
            logger.info("=" * 60)
            logger.info("二分类分析完成！")
            logger.info("=" * 60)
            
            if self.results['best_model']:
                best_model = self.results['best_model']
                logger.info(f"推荐的最佳模型: {best_model['best_model']}")
                logger.info(f"综合性能得分: {best_model['best_score']:.4f}")
                
                # 输出前三名模型
                logger.info("\n性能排名前三的模型:")
                for i, (model, score) in enumerate(best_model['top_models'][:3], 1):
                    logger.info(f"  {i}. {model}: {score:.4f}")
            
            logger.info(f"\n总分析时间: {total_time:.2f} 秒")
            logger.info(f"输出目录: {self.output_dir}")
            logger.info("=" * 60)
            
            return True
            
        except Exception as e:
            logger.error(f"生成摘要报告失败: {e}")
            return False
    
    def _generate_summary_report(self, total_time):
        """
        生成Markdown格式的摘要报告
        """
        report = f"""# 二分类分析摘要报告

## 分析概览

**分析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**数据文件**: {self.data_path.name}
**总分析时间**: {total_time:.2f} 秒

## 数据信息

- **总样本数**: {self.results['data_info']['total_samples']}
- **训练集**: {self.results['data_info']['train_samples']} 样本
- **测试集**: {self.results['data_info']['test_samples']} 样本
- **特征数量**: {self.results['data_info']['features']}

### 类别分布
- **训练集**: 类别0: {self.results['data_info']['class_distribution']['train']['class_0']}, 类别1: {self.results['data_info']['class_distribution']['train']['class_1']}
- **测试集**: 类别0: {self.results['data_info']['class_distribution']['test']['class_0']}, 类别1: {self.results['data_info']['class_distribution']['test']['class_1']}

## 模型训练结果

**训练的模型数量**: {len(self.results['trained_models'])}
**训练的模型**: {', '.join(self.results['trained_models'])}

"""
        
        if self.results['best_model']:
            best_model = self.results['best_model']
            report += f"""## 最佳模型推荐

**推荐模型**: {best_model['best_model']}
**综合得分**: {best_model['best_score']:.4f}
**选择策略**: {best_model['strategy']}

### 性能排名
"""
            for i, (model, score) in enumerate(best_model['top_models'][:5], 1):
                report += f"{i}. **{model}**: {score:.4f}\n"
            
            report += f"""
### 推荐理由

{best_model['selection_reasoning']}

"""
        
        # 添加集成学习结果
        if 'best_ensemble' in self.results:
            best_ensemble = self.results['best_ensemble']
            report += f"""
## 集成学习结果

**最佳集成方法**: {best_ensemble['method']}

### 集成模型性能
- **准确率**: {best_ensemble['metrics']['accuracy']:.4f}
- **精确率**: {best_ensemble['metrics']['precision']:.4f}
- **召回率**: {best_ensemble['metrics']['recall']:.4f}
- **F1分数**: {best_ensemble['metrics']['f1_score']:.4f}
- **AUC**: {best_ensemble['metrics']['auc']:.4f}

"""

        report += f"""## 分析时间统计

- **数据预处理**: {self.results['analysis_time'].get('data_preprocessing', 0):.2f} 秒
- **模型训练**: {self.results['analysis_time'].get('model_training', 0):.2f} 秒
- **模型选择**: {self.results['analysis_time'].get('model_selection', 0):.2f} 秒
- **集成学习**: {self.results['analysis_time'].get('ensemble_learning', 0):.2f} 秒
- **可视化生成**: {self.results['analysis_time'].get('visualization', 0):.2f} 秒

## 输出文件

分析结果保存在以下位置：
- **性能报告**: `reports/performance_report.html`
- **模型比较图表**: `reports/`
- **最佳模型可视化**: `output/{best_model['best_model'] if self.results['best_model'] else 'N/A'}/`
- **SHAP分析**: `output/{best_model['best_model'] if self.results['best_model'] else 'N/A'}/` (如果启用)

## 建议

基于分析结果，建议：
1. 使用推荐的最佳模型进行预测
2. 查看SHAP分析了解模型决策过程
3. 根据业务需求考虑模型的可解释性和性能平衡

---
*报告由二分类分析流程自动生成*
"""
        
        return report
    
    def run_complete_analysis(self, selected_models=None, strategy='balanced'):
        """
        运行完整的分析流程
        
        Args:
            selected_models: 要训练的模型列表
            strategy: 模型选择策略
            
        Returns:
            bool: 分析是否成功完成
        """
        logger.info("🚀 开始完整的二分类分析流程")
        
        # 步骤1: 数据预处理
        if not self.step1_data_preprocessing():
            logger.error("数据预处理失败，分析终止")
            return False
        
        # 步骤2: 模型训练
        if not self.step2_model_training(selected_models):
            logger.error("模型训练失败，分析终止")
            return False
        
        # 步骤3: 最佳模型选择
        if not self.step3_best_model_selection(strategy):
            logger.error("最佳模型选择失败，分析终止")
            return False

        # 步骤4: 集成学习
        if not self.step4_ensemble_learning():
            logger.error("集成学习失败，分析终止")
            return False

        # 步骤5: 可视化和报告
        if not self.step5_visualization_and_reports():
            logger.error("可视化和报告生成失败，分析终止")
            return False

        # 步骤6: 生成摘要报告
        if not self.step6_generate_summary_report():
            logger.error("摘要报告生成失败")
            return False
        
        logger.info("🎉 完整的二分类分析流程成功完成！")
        return True


def run_binary_classification_analysis(data_path, output_dir=None, selected_models=None,
                                     strategy='balanced', enable_tuning=True, enable_shap=True, enable_ensemble=True):
    """
    运行完整二分类分析的便捷函数

    Args:
        data_path: 数据文件路径
        output_dir: 输出目录
        selected_models: 要训练的模型列表
        strategy: 模型选择策略
        enable_tuning: 是否启用超参数调优
        enable_shap: 是否启用SHAP分析
        enable_ensemble: 是否启用集成学习

    Returns:
        bool: 分析是否成功
    """
    pipeline = BinaryClassificationPipeline(
        data_path=data_path,
        output_dir=output_dir,
        enable_tuning=enable_tuning,
        enable_shap=enable_shap,
        enable_ensemble=enable_ensemble
    )
    
    return pipeline.run_complete_analysis(
        selected_models=selected_models,
        strategy=strategy
    )


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="运行完整的二分类分析流程")
    parser.add_argument("--data", type=str, required=True, help="数据文件路径")
    parser.add_argument("--output", type=str, help="输出目录")
    parser.add_argument("--models", type=str, help="要训练的模型，用逗号分隔")
    parser.add_argument("--strategy", type=str, default="balanced", 
                       choices=['performance', 'robustness', 'balanced', 'interpretability'],
                       help="模型选择策略")
    parser.add_argument("--no-tuning", action="store_true", help="禁用超参数调优")
    parser.add_argument("--no-shap", action="store_true", help="禁用SHAP分析")
    
    args = parser.parse_args()
    
    selected_models = None
    if args.models:
        selected_models = [m.strip() for m in args.models.split(',')]
    
    success = run_binary_classification_analysis(
        data_path=args.data,
        output_dir=args.output,
        selected_models=selected_models,
        strategy=args.strategy,
        enable_tuning=not args.no_tuning,
        enable_shap=not args.no_shap
    )
    
    if success:
        print("✅ 二分类分析成功完成！")
    else:
        print("❌ 二分类分析失败！")
