2025-08-08 21:18:19 - training_session_manager - INFO - 初始化训练会话管理器
2025-08-08 21:18:20 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-08 21:18:20 - GUI - INFO - 字体管理器初始化成功，使用英文字体
2025-08-08 21:18:20 - data_exploration - INFO - 数据探索器初始化完成，输出目录: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\data_exploration
2025-08-08 21:18:20 - gui_data_exploration - INFO - 数据探索GUI组件初始化完成
2025-08-08 21:18:20 - GUI - INFO - GUI界面初始化完成
2025-08-08 21:19:05 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 8)
2025-08-08 21:19:05 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-08-08 21:19:05 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50
2025-08-08 21:19:05 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 6
2025-08-08 21:19:05 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 21:19:05 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-08 21:19:05 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 21:19:05 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 6, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x0000026DE2336430>]}
2025-08-08 21:19:06 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9830
2025-08-08 21:19:07 - hyperparameter_tuning - INFO - Trial 5: 发现更好的得分 0.9865
2025-08-08 21:19:08 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9898
2025-08-08 21:19:17 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 21:19:17 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9898
2025-08-08 21:19:18 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 21:19:18 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9898
2025-08-08 21:19:18 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 21:19:18 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9898
2025-08-08 21:19:18 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 21:19:18 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9898
2025-08-08 21:19:18 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 21:19:18 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9898
2025-08-08 21:19:19 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 21:19:19 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9898
2025-08-08 21:19:19 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 116, 'max_depth': 16, 'min_samples_split': 19, 'min_samples_leaf': 8, 'max_features': 'sqrt'}
2025-08-08 21:19:19 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9898
2025-08-08 21:19:19 - hyperparameter_tuning - INFO - 实际执行试验次数: 18/50
2025-08-08 21:19:19 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-08 21:19:19 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-08 21:19:19 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 21:19:19 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250808_211919.html
2025-08-08 21:19:19 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-08 21:19:19 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 21:19:19 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250808_211919.html
2025-08-08 21:19:19 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 14.69 秒
2025-08-08 21:19:24 - training_session_manager - INFO - 创建会话目录结构: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_211924
2025-08-08 21:19:24 - training_session_manager - INFO - 创建训练会话: 训练_nodule2_20250808_211924 (ID: 20250808_211924)
2025-08-08 21:19:24 - training_session_manager - INFO - 创建新会话: 训练_nodule2_20250808_211924
2025-08-08 21:19:24 - session_utils - INFO - 创建新会话: 训练_nodule2_20250808_211924 (ID: 20250808_211924)
2025-08-08 21:19:24 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 8)
2025-08-08 21:19:24 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-08-08 21:19:24 - model_training - INFO - 模型名称: Random Forest
2025-08-08 21:19:24 - model_training - INFO - 准确率: 0.8780
2025-08-08 21:19:24 - model_training - INFO - AUC: 0.9481
2025-08-08 21:19:24 - model_training - INFO - AUPRC: 0.9453
2025-08-08 21:19:24 - model_training - INFO - 混淆矩阵:
2025-08-08 21:19:24 - model_training - INFO - 
[[21  2]
 [ 3 15]]
2025-08-08 21:19:24 - model_training - INFO - 
分类报告:
2025-08-08 21:19:24 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.83      0.86        18

    accuracy                           0.88        41
   macro avg       0.88      0.87      0.88        41
weighted avg       0.88      0.88      0.88        41

2025-08-08 21:19:24 - model_training - INFO - 训练时间: 0.08 秒
2025-08-08 21:19:24 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8780
2025-08-08 21:19:24 - training_session_manager - INFO - 保存模型 RandomForest 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_211924\models\RandomForest_single_211924.joblib
2025-08-08 21:19:24 - model_training - INFO - 模型 RandomForest 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_211924\models\RandomForest_single_211924.joblib
2025-08-08 21:19:24 - model_training - INFO - 模型 RandomForest 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
