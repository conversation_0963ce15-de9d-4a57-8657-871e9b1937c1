#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI可视化修复模块
处理数据不完整时的可视化显示问题
"""

import os
import tkinter as tk
from tkinter import messagebox
from pathlib import Path
from typing import Dict, List, Optional, Any
import logging

# 尝试导入相关模块
try:
    from config import CACHE_PATH, MODEL_NAMES, MODEL_DISPLAY_NAMES
    from logger import get_default_logger
except ImportError:
    # 如果导入失败，使用默认配置
    PROJECT_ROOT = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    CACHE_PATH = PROJECT_ROOT / 'cache'
    MODEL_NAMES = ['DecisionTree', 'RandomForest', 'XGBoost', 'LightGBM', 'CatBoost',
                   'Logistic', 'SVM', 'KNN', 'NaiveBayes', 'NeuralNet']
    MODEL_DISPLAY_NAMES = {name: name for name in MODEL_NAMES}
    
    def get_default_logger(name):
        logger = logging.getLogger(name)
        logger.setLevel(logging.INFO)
        if not logger.handlers:
            handler = logging.StreamHandler()
            handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
            logger.addHandler(handler)
        return logger

logger = get_default_logger("gui_visualization_fix")


class VisualizationFixer:
    """可视化修复器"""
    
    def __init__(self):
        """初始化修复器"""
        self.logger = logger
    
    def get_available_models_with_status(self) -> Dict[str, Dict[str, Any]]:
        """获取可用模型及其状态"""
        models_status = {}
        
        try:
            from joblib import load
            
            for model_name in MODEL_NAMES:
                cache_file = CACHE_PATH / f"{model_name}_results.joblib"
                
                if cache_file.exists():
                    try:
                        data = load(cache_file)
                        
                        if isinstance(data, dict) and 'model' in data and data['model'] is not None:
                            # 检查数据完整性
                            has_test_data = (
                                data.get('y_true') is not None and 
                                data.get('y_pred') is not None and 
                                data.get('X_test') is not None
                            )
                            
                            models_status[model_name] = {
                                'available': True,
                                'has_test_data': has_test_data,
                                'model_object': data['model'],
                                'data': data,
                                'display_name': MODEL_DISPLAY_NAMES.get(model_name, model_name)
                            }
                            
                            if has_test_data:
                                # 计算基本性能指标
                                try:
                                    from sklearn.metrics import accuracy_score
                                    accuracy = accuracy_score(data['y_true'], data['y_pred'])
                                    models_status[model_name]['accuracy'] = accuracy
                                except:
                                    models_status[model_name]['accuracy'] = None
                        
                    except Exception as e:
                        self.logger.warning(f"加载模型 {model_name} 失败: {e}")
                        models_status[model_name] = {
                            'available': False,
                            'has_test_data': False,
                            'error': str(e)
                        }
        
        except Exception as e:
            self.logger.error(f"获取模型状态失败: {e}")
        
        return models_status
    
    def create_model_info_display(self, parent_widget) -> tk.Frame:
        """创建模型信息显示框架"""
        info_frame = tk.Frame(parent_widget)
        
        # 标题
        title_label = tk.Label(info_frame, text="可用模型状态", font=("Arial", 12, "bold"))
        title_label.pack(pady=5)
        
        # 获取模型状态
        models_status = self.get_available_models_with_status()
        
        if not models_status:
            no_models_label = tk.Label(info_frame, text="没有找到可用的模型", fg="red")
            no_models_label.pack(pady=10)
            return info_frame
        
        # 创建滚动框架
        canvas = tk.Canvas(info_frame, height=200)
        scrollbar = tk.Scrollbar(info_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 显示每个模型的状态
        for model_name, status in models_status.items():
            model_frame = tk.Frame(scrollable_frame, relief=tk.RIDGE, bd=1)
            model_frame.pack(fill=tk.X, padx=5, pady=2)
            
            if status['available']:
                if status['has_test_data']:
                    status_color = "green"
                    status_text = "✅ 完整数据"
                    if 'accuracy' in status and status['accuracy'] is not None:
                        status_text += f" (准确率: {status['accuracy']:.3f})"
                else:
                    status_color = "orange"
                    status_text = "⚠️ 仅模型对象"
            else:
                status_color = "red"
                status_text = "❌ 不可用"
            
            model_label = tk.Label(
                model_frame, 
                text=f"{status.get('display_name', model_name)}: {status_text}",
                fg=status_color,
                anchor="w"
            )
            model_label.pack(fill=tk.X, padx=5, pady=2)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        return info_frame
    
    def show_visualization_options_dialog(self, parent=None):
        """显示可视化选项对话框"""
        dialog = tk.Toplevel(parent)
        dialog.title("模型可视化选项")
        dialog.geometry("600x500")
        dialog.resizable(True, True)
        
        if parent:
            dialog.transient(parent)
            dialog.grab_set()
        
        # 主框架
        main_frame = tk.Frame(dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 说明文本
        info_text = tk.Text(main_frame, height=8, wrap=tk.WORD)
        info_text.pack(fill=tk.X, pady=(0, 10))
        
        info_content = """模型可视化状态说明：

✅ 完整数据：模型包含完整的测试数据，支持所有类型的可视化分析
⚠️ 仅模型对象：模型对象存在但缺少测试数据，支持有限的可视化
❌ 不可用：模型文件损坏或不存在

对于"仅模型对象"的模型，您可以：
1. 重新训练模型以获得完整数据
2. 使用模型对象进行基本的参数查看
3. 查看模型的特征重要性（如果支持）"""
        
        info_text.insert(tk.END, info_content)
        info_text.config(state=tk.DISABLED)
        
        # 模型状态显示
        status_frame = self.create_model_info_display(main_frame)
        status_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 操作按钮
        button_frame = tk.Frame(main_frame)
        button_frame.pack(fill=tk.X)
        
        def refresh_status():
            # 刷新模型状态
            for widget in status_frame.winfo_children():
                widget.destroy()
            
            new_status_frame = self.create_model_info_display(main_frame)
            new_status_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        def show_model_details():
            self.show_detailed_model_info(dialog)
        
        tk.Button(button_frame, text="刷新状态", command=refresh_status).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="详细信息", command=show_model_details).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="关闭", command=dialog.destroy).pack(side=tk.RIGHT, padx=5)
    
    def show_detailed_model_info(self, parent=None):
        """显示详细的模型信息"""
        models_status = self.get_available_models_with_status()
        
        detail_window = tk.Toplevel(parent)
        detail_window.title("详细模型信息")
        detail_window.geometry("700x600")
        
        # 创建文本框显示详细信息
        text_widget = tk.Text(detail_window, wrap=tk.WORD)
        scrollbar = tk.Scrollbar(detail_window, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)
        
        # 生成详细信息
        detail_text = "详细模型信息报告\n" + "=" * 50 + "\n\n"
        
        for model_name, status in models_status.items():
            detail_text += f"模型: {status.get('display_name', model_name)}\n"
            detail_text += "-" * 30 + "\n"
            
            if status['available']:
                detail_text += f"状态: 可用\n"
                detail_text += f"数据完整性: {'完整' if status['has_test_data'] else '不完整'}\n"
                
                if 'accuracy' in status and status['accuracy'] is not None:
                    detail_text += f"准确率: {status['accuracy']:.4f}\n"
                
                # 模型参数信息
                try:
                    model_obj = status['model_object']
                    if hasattr(model_obj, 'get_params'):
                        params = model_obj.get_params()
                        detail_text += f"主要参数: {list(params.keys())[:5]}\n"
                    
                    if hasattr(model_obj, 'feature_importances_'):
                        detail_text += "支持特征重要性分析\n"
                    
                except Exception as e:
                    detail_text += f"参数获取失败: {e}\n"
            else:
                detail_text += f"状态: 不可用\n"
                if 'error' in status:
                    detail_text += f"错误: {status['error']}\n"
            
            detail_text += "\n"
        
        text_widget.insert(tk.END, detail_text)
        text_widget.config(state=tk.DISABLED)
        
        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def fix_gui_visualization_issues(self, main_gui):
        """修复GUI可视化问题"""
        try:
            # 获取模型状态
            models_status = self.get_available_models_with_status()
            available_models = [name for name, status in models_status.items() if status['available']]
            
            # 更新GUI中的模型选择
            if hasattr(main_gui, 'viz_model_var') and hasattr(main_gui, 'viz_model_combo'):
                current_value = main_gui.viz_model_var.get()
                main_gui.viz_model_combo['values'] = available_models
                
                if current_value not in available_models and available_models:
                    main_gui.viz_model_var.set(available_models[0])
                elif not available_models:
                    main_gui.viz_model_var.set("")
            
            # 显示状态信息
            if hasattr(main_gui, 'log_message'):
                complete_models = [name for name, status in models_status.items() 
                                 if status['available'] and status['has_test_data']]
                partial_models = [name for name, status in models_status.items() 
                                if status['available'] and not status['has_test_data']]
                
                main_gui.log_message(f"模型状态更新完成")
                main_gui.log_message(f"完整数据模型: {len(complete_models)} 个")
                main_gui.log_message(f"部分数据模型: {len(partial_models)} 个")
                
                if complete_models:
                    main_gui.log_message(f"可完整可视化: {', '.join(complete_models)}")
                if partial_models:
                    main_gui.log_message(f"有限可视化: {', '.join(partial_models)}")
            
            return len(available_models) > 0
            
        except Exception as e:
            self.logger.error(f"修复GUI可视化问题失败: {e}")
            return False


def create_visualization_fixer():
    """创建可视化修复器实例"""
    return VisualizationFixer()
