#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成学习功能完整使用示例（已归档至 tools/demos/）
演示如何使用新实现的集成学习功能
"""

import sys
from pathlib import Path
import numpy as np
import pandas as pd
from sklearn.datasets import make_classification
from sklearn.model_selection import train_test_split

# 添加代码路径
sys.path.append(str(Path(__file__).parents[2] / 'code'))

from model_ensemble import run_ensemble_pipeline, EnsembleClassifier
from plot_ensemble import visualize_ensemble_results
from binary_classification_pipeline import run_binary_classification_analysis
from logger import get_logger

logger = get_logger(__name__)


def create_sample_data():
    """创建示例数据"""
    logger.info("创建示例数据...")

    # 生成二分类数据
    X, y = make_classification(
        n_samples=1000,
        n_features=20,
        n_informative=15,
        n_redundant=5,
        n_clusters_per_class=1,
        random_state=42
    )

    # 创建DataFrame
    feature_names = [f'feature_{i}' for i in range(X.shape[1])]
    df = pd.DataFrame(X, columns=feature_names)
    df['label'] = y

    # 保存到CSV文件
    data_path = Path(__file__).parents[2] / 'sample_data.csv'
    df.to_csv(data_path, index=False)

    logger.info(f"示例数据已保存到: {data_path}")
    logger.info(f"数据形状: {df.shape}")
    logger.info(f"类别分布: {np.bincount(y)}")

    return data_path


def example_1_basic_ensemble():
    """示例1: 基础集成学习"""
    logger.info("=" * 60)
    logger.info("示例1: 基础集成学习")
    logger.info("=" * 60)

    # 创建示例数据
    data_path = create_sample_data()

    # 加载数据
    from data_preprocessing import load_and_preprocess_data
    X_train, X_test, y_train, y_test = load_and_preprocess_data(str(data_path))

    # 运行集成学习
    model_names = ['RandomForest', 'XGBoost', 'LightGBM']

    ensemble_results = run_ensemble_pipeline(
        X_train=X_train,
        y_train=y_train,
        X_test=X_test,
        y_test=y_test,
        model_names=model_names,
        ensemble_methods=['voting', 'stacking'],
        save_results=True,
        output_dir=Path(__file__).parents[2] / 'output' / 'ensemble_example',
        enable_shap=False  # 为了加快演示速度
    )

    if ensemble_results:
        logger.info("集成学习完成！")

        # 显示结果
        for name, result in ensemble_results.items():
            metrics = result['metrics']
            logger.info(f"{name}: 准确率={metrics['accuracy']:.4f}, F1={metrics['f1_score']:.4f}")

        # 生成可视化
        logger.info("生成可视化图表...")
        visualize_ensemble_results(
            ensemble_results=ensemble_results,
            X_train=X_train,
            y_train=y_train,
            output_dir=Path(__file__).parents[2] / 'output' / 'ensemble_example' / 'visualizations'
        )

        return True
    else:
        logger.error("集成学习失败")
        return False


def example_2_custom_ensemble():
    """示例2: 自定义集成分类器"""
    logger.info("=" * 60)
    logger.info("示例2: 自定义集成分类器")
    logger.info("=" * 60)

    # 创建示例数据
    data_path = create_sample_data()

    # 加载数据
    from data_preprocessing import load_and_preprocess_data
    X_train, X_test, y_train, y_test = load_and_preprocess_data(str(data_path))

    # 创建基础模型
    from model_ensemble import create_base_models_from_names
    base_models = create_base_models_from_names(
        ['RandomForest', 'XGBoost'],
        X_train, y_train, X_test, y_test
    )

    if not base_models:
        logger.error("无法创建基础模型")
        return False

    # 测试不同的集成方法
    ensemble_methods = ['voting', 'bagging', 'stacking']

    for method in ensemble_methods:
        logger.info(f"测试集成方法: {method}")

        try:
            # 创建集成分类器
            ensemble = EnsembleClassifier(
                base_models=base_models,
                ensemble_method=method,
                random_state=42
            )

            # 训练
            ensemble.fit(X_train, y_train)

            # 预测
            y_pred = ensemble.predict(X_test)
            accuracy = np.mean(y_pred == y_test)

            logger.info(f"  {method} 准确率: {accuracy:.4f}")

        except Exception as e:
            logger.error(f"  {method} 失败: {e}")

    return True


def example_3_complete_pipeline():
    """示例3: 完整的二分类分析管道（包含集成学习）"""
    logger.info("=" * 60)
    logger.info("示例3: 完整的二分类分析管道")
    logger.info("=" * 60)

    # 创建示例数据
    data_path = create_sample_data()

    # 运行完整的二分类分析（包含集成学习）
    success = run_binary_classification_analysis(
        data_path=str(data_path),
        output_dir=str(Path(__file__).parents[2] / 'output' / 'complete_pipeline'),
        selected_models=['RandomForest', 'XGBoost', 'LightGBM'],
        strategy='balanced',
        enable_tuning=False,  # 为了加快演示速度
        enable_shap=False,    # 为了加快演示速度
        enable_ensemble=True  # 启用集成学习
    )

    if success:
        logger.info("完整分析管道执行成功！")
        logger.info(f"结果保存在: {Path(__file__).parents[2] / 'output' / 'complete_pipeline'}")
        return True
    else:
        logger.error("完整分析管道执行失败")
        return False


if __name__ == '__main__':
    example_1_basic_ensemble()
    example_2_custom_ensemble()
    example_3_complete_pipeline()

