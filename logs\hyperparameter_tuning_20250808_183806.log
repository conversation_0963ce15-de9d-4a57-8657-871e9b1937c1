2025-08-08 18:38:06 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 3
2025-08-08 18:38:06 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-08 18:38:06 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 18:38:06 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 3, 'n_jobs': 1, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x000001D5B9014D60>]}
2025-08-08 18:38:07 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.8287
2025-08-08 18:38:08 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.8694
2025-08-08 18:38:08 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 258, 'max_depth': 8, 'min_samples_split': 5, 'min_samples_leaf': 4, 'max_features': 'log2'}
2025-08-08 18:38:08 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.8694
2025-08-08 18:38:08 - hyperparameter_tuning - INFO - 实际执行试验次数: 3/3
2025-08-08 18:38:09 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 18:38:09 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250808_183808.html
2025-08-08 18:38:09 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 18:38:09 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250808_183809.html
2025-08-08 18:38:09 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 2.31 秒
