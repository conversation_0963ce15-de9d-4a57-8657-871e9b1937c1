2025-08-08 21:27:26 - numexpr.utils - INFO - Note: NumExpr detected 16 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 8.
2025-08-08 21:27:26 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-08-08 21:27:27 - training_session_manager - INFO - 初始化训练会话管理器
2025-08-08 21:27:28 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-08 21:27:30 - main - INFO - 已确保输出目录结构存在: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\output
2025-08-08 21:27:30 - main - INFO - 已保存数据文件路径到缓存: sample_data.csv
2025-08-08 21:27:30 - main - INFO - 训练和优化 RandomForest
2025-08-08 21:27:30 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50
2025-08-08 21:27:30 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-08 21:27:30 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-08 21:27:30 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-08 21:27:30 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x000001757D8E0860>]}
2025-08-08 21:27:31 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.8287
2025-08-08 21:27:32 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.8694
2025-08-08 21:27:39 - hyperparameter_tuning - INFO - Trial 11: 发现更好的得分 0.8772
2025-08-08 21:27:49 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-08 21:27:49 - hyperparameter_tuning - INFO - 当前最佳得分: 0.8772
2025-08-08 21:27:49 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 256, 'max_depth': 20, 'min_samples_split': 2, 'min_samples_leaf': 1, 'max_features': 'log2'}
2025-08-08 21:27:49 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.8772
2025-08-08 21:27:49 - hyperparameter_tuning - INFO - 实际执行试验次数: 22/50
2025-08-08 21:27:49 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-08 21:27:49 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-08 21:27:49 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 21:27:49 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250808_212749.html
2025-08-08 21:27:50 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-08 21:27:50 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-08 21:27:50 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250808_212750.html
2025-08-08 21:27:50 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 19.26 秒
2025-08-08 21:27:50 - main - INFO - 使用最佳参数重新训练 RandomForest, 最佳分数: 0.8772
2025-08-08 21:27:50 - model_training - INFO - 模型名称: Random Forest
2025-08-08 21:27:50 - model_training - INFO - 准确率: 0.7000
2025-08-08 21:27:50 - model_training - INFO - AUC: 0.8313
2025-08-08 21:27:50 - model_training - INFO - AUPRC: 0.8569
2025-08-08 21:27:50 - model_training - INFO - 混淆矩阵:
2025-08-08 21:27:50 - model_training - INFO - 
[[13  7]
 [ 5 15]]
2025-08-08 21:27:50 - model_training - INFO - 
分类报告:
2025-08-08 21:27:50 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.72      0.65      0.68        20
           1       0.68      0.75      0.71        20

    accuracy                           0.70        40
   macro avg       0.70      0.70      0.70        40
weighted avg       0.70      0.70      0.70        40

2025-08-08 21:27:50 - model_training - INFO - 训练时间: 0.25 秒
2025-08-08 21:27:50 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.7000
2025-08-08 21:27:50 - model_training - INFO - 模型 RandomForest 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-08 21:27:50 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
