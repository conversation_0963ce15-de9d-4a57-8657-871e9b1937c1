#!/usr/bin/env python3
# 验证 ModelTrainer 构造方式，并测试 AUC 计算路径
import numpy as np
from model_training import ModelTrainer
from sklearn.tree import DecisionTreeClassifier
from sklearn.svm import SVC

np.random.seed(42)
X = np.random.randn(120, 6)
y = np.random.choice([0,1], size=120)
X_train, X_test = X[:100], X[100:]
y_train, y_test = y[:100], y[100:]

# DecisionTree（有predict_proba）
trainer_dt = ModelTrainer('DecisionTree', DecisionTreeClassifier, default_params={})
res_dt = trainer_dt.train_and_evaluate(X_train, y_train, X_test, y_test)
print('DT done')

# SVM（有decision_function）
trainer_svm = ModelTrainer('SVM', SVC, default_params={'probability': False})
res_svm = trainer_svm.train_and_evaluate(X_train, y_train, X_test, y_test)
print('SVM done')

