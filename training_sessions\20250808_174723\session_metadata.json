{"session_id": "20250808_174723", "session_name": "训练_nodule2_20250808_174723", "description": "自动创建的训练会话，基于数据文件: nodule2", "created_time": "2025-08-08T17:47:23.349629", "last_modified": "2025-08-08T17:47:25.219566", "trained_models": [{"model_name": "DecisionTree", "model_type": "single", "filename": "DecisionTree_single_174723.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250808_174723\\models\\DecisionTree_single_174723.joblib", "save_time": "2025-08-08T17:47:23.390300"}, {"model_name": "RandomForest", "model_type": "single", "filename": "RandomForest_single_174723.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250808_174723\\models\\RandomForest_single_174723.joblib", "save_time": "2025-08-08T17:47:23.497562"}, {"model_name": "XGBoost", "model_type": "single", "filename": "XGBoost_single_174723.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250808_174723\\models\\XGBoost_single_174723.joblib", "save_time": "2025-08-08T17:47:23.594955"}, {"model_name": "LightGBM", "model_type": "single", "filename": "LightGBM_single_174723.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250808_174723\\models\\LightGBM_single_174723.joblib", "save_time": "2025-08-08T17:47:23.659541"}, {"model_name": "CatBoost", "model_type": "single", "filename": "CatBoost_single_174724.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250808_174723\\models\\CatBoost_single_174724.joblib", "save_time": "2025-08-08T17:47:24.817410"}, {"model_name": "Logistic", "model_type": "single", "filename": "Logistic_single_174724.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250808_174723\\models\\Logistic_single_174724.joblib", "save_time": "2025-08-08T17:47:24.848819"}, {"model_name": "SVM", "model_type": "single", "filename": "SVM_single_174724.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250808_174723\\models\\SVM_single_174724.joblib", "save_time": "2025-08-08T17:47:24.875062"}, {"model_name": "KNN", "model_type": "single", "filename": "KNN_single_174724.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250808_174723\\models\\KNN_single_174724.joblib", "save_time": "2025-08-08T17:47:24.901910"}, {"model_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "model_type": "single", "filename": "NaiveBayes_single_174724.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250808_174723\\models\\NaiveBayes_single_174724.joblib", "save_time": "2025-08-08T17:47:24.928038"}, {"model_name": "NeuralNet", "model_type": "single", "filename": "NeuralNet_single_174725.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250808_174723\\models\\NeuralNet_single_174725.joblib", "save_time": "2025-08-08T17:47:25.211379"}], "ensemble_results": [], "data_files": [], "plots": [], "logs": [], "status": "created"}